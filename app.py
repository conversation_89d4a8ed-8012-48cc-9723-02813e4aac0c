import os
import random
import time
import uuid
import asyncio
from flask import Flask, request, render_template, flash, redirect, url_for, send_file
from werkzeug.utils import secure_filename
from gtts import gTTS

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# Configuration
UPLOAD_FOLDER = 'uploads'
AUDIO_FOLDER = 'audio'
ALLOWED_EXTENSIONS = {'txt'}

# Create directories if they don't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(AUDIO_FOLDER, exist_ok=True)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['AUDIO_FOLDER'] = AUDIO_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# TTS Configuration
TTS_PROVIDERS = {
    "google": {
        "function": "generate_google_tts",
        "voices": ["en-US", "en-GB", "en-AU", "en-IN", "en-CA"]
    },
    "edge": {
        "function": "generate_edge_tts",
        "voices": [
            "en-US-AriaNeural", "en-US-GuyNeural", "en-US-JennyNeural",
            "en-GB-SoniaNeural", "en-GB-RyanNeural", "en-AU-NatashaNeural",
            "en-AU-WilliamNeural", "en-CA-ClaraNeural", "en-CA-LiamNeural"
        ]
    }
}

# Initialize TTS
try:
    # Test gTTS availability
    test_tts = gTTS(text="test", lang='en')
    tts_available = True
    print("✓ Text-to-Speech engine initialized successfully")
except Exception as e:
    print(f"Warning: TTS not available: {e}")
    print("The app will work without TTS functionality")
    tts_available = False

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        flash('No file selected')
        return redirect(request.url)
    
    file = request.files['file']
    
    if file.filename == '':
        flash('No file selected')
        return redirect(request.url)
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # Read the text content
        with open(filepath, 'r', encoding='utf-8') as f:
            text_content = f.read()

        # Convert text to speech if TTS is available
        audio_filename = None
        if tts_available and text_content.strip():
            try:
                audio_filename = convert_text_to_speech(text_content, filename)
            except Exception as e:
                flash(f'Text uploaded successfully, but TTS conversion failed: {str(e)}')

        return render_template('result.html',
                             text_content=text_content,
                             original_filename=filename,
                             audio_file=audio_filename,
                             tts_available=tts_available)
    else:
        flash('Invalid file type. Please upload a .txt file.')
        return redirect(url_for('index'))

def generate_tts(text, voice=None, provider=None, original_filename="output"):
    """
    Generate text-to-speech audio with random or specified voice and provider
    """
    # If no provider specified, choose randomly
    if not provider:
        provider = random.choice(list(TTS_PROVIDERS.keys()))

    # If provider not valid, default to google
    if provider not in TTS_PROVIDERS:
        provider = "google"

    # If no voice specified, choose randomly from the provider's voices
    if not voice:
        voice = random.choice(TTS_PROVIDERS[provider]["voices"])

    # Generate unique filename based on timestamp and random ID
    timestamp = int(time.time())
    random_id = str(uuid.uuid4())[:8]
    filename = f"{os.path.splitext(original_filename)[0]}_{timestamp}_{random_id}.mp3"
    output_path = os.path.join(app.config['AUDIO_FOLDER'], filename)

    # Call the appropriate TTS function based on provider
    if provider == "google":
        return generate_google_tts(text, voice, output_path, filename)
    elif provider == "edge":
        return generate_edge_tts(text, voice, output_path, filename)
    else:
        # Fallback to Google TTS
        return generate_google_tts(text, "en-US", output_path, filename)

def generate_google_tts(text, lang="en-US", output_path="output/voice.mp3", filename="voice.mp3"):
    """Generate TTS using Google's gTTS"""
    try:
        # If lang is not a valid language code, default to en-US
        if lang not in ["en-US", "en-GB", "en-AU", "en-IN", "en-CA"]:
            lang = "en-US"

        # Convert language code to gTTS format (just 'en' for English)
        gtts_lang = lang.split('-')[0]
        tld = lang.split('-')[1].lower() if '-' in lang else 'com'

        # Create gTTS object
        tts = gTTS(text=text, lang=gtts_lang, tld=tld)
        tts.save(output_path)
        print(f"Generated Google TTS with voice: {lang}")
        return filename
    except Exception as e:
        print(f"Error generating Google TTS: {e}")
        # Fallback to basic gTTS
        tts = gTTS(text=text, lang='en')
        tts.save(output_path)
        return filename

def generate_edge_tts(text, voice="en-US-AriaNeural", output_path="output/voice.mp3", filename="voice.mp3"):
    """Generate TTS using Microsoft Edge TTS"""
    try:
        # Import edge_tts
        import edge_tts

        # Define async function to generate TTS
        async def _generate_edge_tts():
            communicate = edge_tts.Communicate(text, voice)
            await communicate.save(output_path)

        # Run the async function
        asyncio.run(_generate_edge_tts())

        print(f"Generated Edge TTS with voice: {voice}")
        return filename
    except Exception as e:
        print(f"Error generating Edge TTS: {e}, falling back to Google TTS")
        # Fallback to Google TTS
        return generate_google_tts(text, "en-US", output_path, filename)

def convert_text_to_speech(text, original_filename):
    """Convert text to speech using the TTS system"""
    try:
        return generate_tts(text, provider="google", original_filename=original_filename)
    except Exception as e:
        raise Exception(f"Failed to convert text to speech: {str(e)}")

@app.route('/audio/<filename>')
def serve_audio(filename):
    """Serve audio files"""
    return send_file(os.path.join(app.config['AUDIO_FOLDER'], filename))

@app.route('/download/<filename>')
def download_audio(filename):
    """Download audio files"""
    return send_file(os.path.join(app.config['AUDIO_FOLDER'], filename), as_attachment=True)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=True)
